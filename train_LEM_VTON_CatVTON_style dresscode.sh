#!/bin/bash

# 使用DressCode数据集的LEM VTON训练脚本 - 四卡accelerate加速训练
# 只训练下装(lower_body)和连衣裙(dresses)类别，支持4卡分布式训练

echo "🚀 启动DressCode VTON训练 (下装+连衣裙) - 四卡accelerate 20万步训练"
echo "=================================================================="

# 设置NCCL环境变量防止通讯超时
export NCCL_TIMEOUT=1800                    # 30分钟超时
export NCCL_IB_DISABLE=1                    # 禁用InfiniBand，使用以太网
export NCCL_SOCKET_IFNAME=^docker0,lo       # 排除docker和loopback接口
export NCCL_DEBUG=INFO                      # 启用NCCL调试信息
export CUDA_LAUNCH_BLOCKING=0               # 异步CUDA启动
export NCCL_P2P_DISABLE=1                   # 禁用P2P通信，提高稳定性

echo "🔧 NCCL环境变量配置:"
echo "  - NCCL_TIMEOUT: $NCCL_TIMEOUT"
echo "  - NCCL_IB_DISABLE: $NCCL_IB_DISABLE"
echo "  - NCCL_P2P_DISABLE: $NCCL_P2P_DISABLE"
echo "=================================================================="

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate IMAGGarment_standard

# 设置基本参数
DATA_ROOT="/home2/wangyuanpeng/project/VTON/dataset_dc"
OUTPUT_DIR="./checkpoints_dresscode_lower_dress_200k_4gpu"
PRETRAINED_MODEL="/home2/wangyuanpeng/.cache/huggingface/hub/models--runwayml--stable-diffusion-inpainting/snapshots/8a4288a76071f7280aedbdb3253bdb9e9d5d84bb"
# CatVTON预训练权重
CATVTON_WEIGHTS="/home2/wangyuanpeng/project/VTON/amymodel/lem/IMAGGarment-1-main/catvton_ckpt/dcckpt/model (1).safetensors"
# 离线 CLIP 特征目录
CLIP_DIR="/home2/wangyuanpeng/project/VTON/amymodel/IMAGGarment-1-main/clip_features"

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 四卡训练参数 - 20万步训练配置 (优化显存利用率)
BATCH_SIZE=4                    # 每卡batch size，4卡总共16 (适合DressCode数据集)
LEARNING_RATE=1e-4
WEIGHT_DECAY=1e-2
MAX_TRAIN_STEPS=200000          # 总训练步数：20万步
SAVE_STEPS=5000                 # 每5千步保存一次
NUM_EPOCHS=1000                 # 设置足够大的epoch数，实际以max_train_steps为准

# 图像尺寸
HEIGHT=512
WIDTH=384

# 掩码处理参数 (CatVTON style)
BLUR_MASK="--blur_mask"      # 启用掩码模糊
BLUR_RADIUS=9                # 高斯模糊半径

# 重要节点保存配置
MILESTONE_STEPS="1000,5000,10000,20000,50000,100000,150000,200000"  # 重要节点步数

# 四卡分布式训练参数
NUM_GPUS=4                      # GPU数量
NUM_PROCESSES=4                 # 进程数量
MAIN_PROCESS_PORT=29502         # 主进程端口
DATASET_TYPE="dresscode"        # 数据集类型
CATEGORIES="lower_body,dresses" # 只训练下装和连衣裙
PHASE="train"
ORDER="paired"
I_DROP_RATE=0.1
MIXED_PRECISION="bf16"          # 使用bf16混合精度
DATALOADER_NUM_WORKERS=4

echo "📊 四卡DressCode训练配置:"
echo "  数据根路径: $DATA_ROOT"
echo "  数据集类型: $DATASET_TYPE"
echo "  训练类别: $CATEGORIES"
echo "  输出目录: $OUTPUT_DIR"
echo "  预训练模型: $PRETRAINED_MODEL"
echo "  CatVTON权重: $CATVTON_WEIGHTS"
echo "  图像尺寸: ${HEIGHT}x${WIDTH}"
echo "  GPU数量: $NUM_GPUS"
echo "  进程数量: $NUM_PROCESSES"
echo "  每卡批次大小: $BATCH_SIZE (总批次: $((BATCH_SIZE * NUM_GPUS)))"
echo "  学习率: $LEARNING_RATE"
echo "  总训练步数: $MAX_TRAIN_STEPS"
echo "  保存间隔: 每 $SAVE_STEPS 步"
echo "  掩码模糊: 启用 (半径=$BLUR_RADIUS)"
echo "  混合精度: $MIXED_PRECISION"
echo "  主进程端口: $MAIN_PROCESS_PORT"
echo ""

# 检查GPU状态
echo "🔍 GPU状态检查:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits
echo ""

# 检查数据集是否存在
if [ ! -d "$DATA_ROOT" ]; then
    echo "❌ 错误: 数据集目录不存在: $DATA_ROOT"
    exit 1
fi

if [ ! -d "$DATA_ROOT/lower_body" ]; then
    echo "❌ 错误: 下装目录不存在: $DATA_ROOT/lower_body"
    exit 1
fi

if [ ! -d "$DATA_ROOT/dresses" ]; then
    echo "❌ 错误: 连衣裙目录不存在: $DATA_ROOT/dresses"
    exit 1
fi

if [ ! -f "$CATVTON_WEIGHTS" ]; then
    echo "❌ 错误: CatVTON权重文件不存在: $CATVTON_WEIGHTS"
    exit 1
fi

echo "✅ DressCode数据集路径验证通过"
echo "✅ CatVTON权重文件验证通过"

# 启动四卡accelerate训练
echo "🚀 开始四卡accelerate训练..."
echo "=================================================================="

CUDA_VISIBLE_DEVICES=0,1,2,3 accelerate launch \
    --num_processes $NUM_PROCESSES \
    --mixed_precision $MIXED_PRECISION \
    --num_machines 1 \
    --dynamo_backend "no" \
    --main_process_port $MAIN_PROCESS_PORT \
    train_LEM_VTON_dresscode.py \
    --pretrained_model_name_or_path $PRETRAINED_MODEL \
    --data_root_path $DATA_ROOT \
    --dataset_type $DATASET_TYPE \
    --categories $CATEGORIES \
    --resume_catvton_weights "$CATVTON_WEIGHTS" \
    --output_dir $OUTPUT_DIR \
    --logging_dir "$OUTPUT_DIR/logs" \
    --height $HEIGHT \
    --width $WIDTH \
    --learning_rate $LEARNING_RATE \
    --weight_decay $WEIGHT_DECAY \
    --max_train_steps $MAX_TRAIN_STEPS \
    --num_train_epochs $NUM_EPOCHS \
    --train_batch_size $BATCH_SIZE \
    --dataloader_num_workers $DATALOADER_NUM_WORKERS \
    --save_steps $SAVE_STEPS \
    --mixed_precision $MIXED_PRECISION \
    --report_to tensorboard \
    --i_drop_rate $I_DROP_RATE \
    --phase $PHASE \
    --order $ORDER \
    $BLUR_MASK \
    --blur_radius $BLUR_RADIUS \
    --clip_features_dir $CLIP_DIR

echo ""
echo "🎉 四卡accelerate 20万步训练完成！"
echo "=================================================================="
echo "📊 检查点保存在: $OUTPUT_DIR"
echo "📈 TensorBoard日志: $OUTPUT_DIR/logs"
echo "🎯 重要节点检查点: milestone-1000, milestone-5000, milestone-10000, ..."
echo "✨ CatVTON格式权重: catvton_attention_weights.safetensors"
echo "🚀 训练配置: 4 GPU × batch_size=$BATCH_SIZE = 总batch_size=$((BATCH_SIZE * NUM_GPUS)) (最大化显存利用)"
echo ""
echo "🚀 使用训练好的权重进行推理:"
echo "python test_pure_catvton.py --attn_ckpt $OUTPUT_DIR/final_model/catvton_attention_weights.safetensors"
echo ""
echo "📊 查看训练日志:"
echo "tensorboard --logdir $OUTPUT_DIR/logs"