#!/bin/bash

# 使用tmux启动四卡accelerate训练 - 可恢复会话
# 使用方法：
# 1. 启动训练：./start_training_tmux.sh
# 2. 分离会话：Ctrl+B, 然后按D
# 3. 重新连接：tmux attach -t vton_training
# 4. 查看会话：tmux list-sessions

echo "🚀 使用tmux启动四卡accelerate训练 (batch_size=8)"
echo "=================================================================="

# 检查tmux是否安装
if ! command -v tmux &> /dev/null; then
    echo "❌ tmux未安装，正在安装..."
    sudo apt-get update && sudo apt-get install -y tmux
fi

# 会话名称
SESSION_NAME="vton_training"

# 检查会话是否已存在
if tmux has-session -t $SESSION_NAME 2>/dev/null; then
    echo "⚠️  会话 '$SESSION_NAME' 已存在"
    echo "选择操作："
    echo "1. 连接到现有会话 (tmux attach -t $SESSION_NAME)"
    echo "2. 杀死现有会话并创建新会话"
    read -p "请选择 (1/2): " choice
    
    if [ "$choice" = "2" ]; then
        echo "🔄 杀死现有会话..."
        tmux kill-session -t $SESSION_NAME
    else
        echo "🔗 连接到现有会话..."
        tmux attach -t $SESSION_NAME
        exit 0
    fi
fi

# 创建新的tmux会话并启动训练
echo "🆕 创建新的tmux会话: $SESSION_NAME"
echo "📝 使用说明："
echo "  - 分离会话: Ctrl+B, 然后按 D"
echo "  - 重新连接: tmux attach -t $SESSION_NAME"
echo "  - 查看会话: tmux list-sessions"
echo "  - 杀死会话: tmux kill-session -t $SESSION_NAME"
echo ""
echo "⏰ 3秒后启动训练..."
sleep 3

# 创建tmux会话并运行训练脚本
tmux new-session -d -s $SESSION_NAME -c "$(pwd)" "./train_LEM_VTON_CatVTON_style.sh"

echo "✅ 训练已在tmux会话中启动！"
echo ""
echo "🔗 连接到训练会话："
echo "tmux attach -t $SESSION_NAME"
echo ""
echo "📊 查看GPU状态："
echo "watch -n 1 nvidia-smi"
echo ""
echo "📈 查看训练日志："
echo "tail -f checkpoints_vton_catvton_style_200k_4gpu/logs/events.out.tfevents.*"
