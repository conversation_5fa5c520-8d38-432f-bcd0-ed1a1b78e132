#!/usr/bin/env python3
"""
检查checkpoint中的投影层权重
"""

import torch
import os

def check_checkpoint_projection_weights(checkpoint_path):
    """检查checkpoint中的投影层权重"""
    print(f"🔍 检查checkpoint: {checkpoint_path}")
    print("=" * 80)

    if not os.path.exists(checkpoint_path):
        print(f"❌ Checkpoint路径不存在: {checkpoint_path}")
        return False

    # 检查是否是目录
    if os.path.isdir(checkpoint_path):
        print("📁 Checkpoint是目录，检查内部文件...")
        files = os.listdir(checkpoint_path)
        print(f"📋 目录内容: {files}")

        # 检查可能包含投影层权重的文件
        weight_files = []
        for file in files:
            if file.endswith('.pth') or file.endswith('.safetensors') or file.endswith('.bin'):
                weight_files.append(file)

        print(f"🎯 权重文件: {weight_files}")

        # 检查每个权重文件
        found_projections = False
        for weight_file in weight_files:
            file_path = os.path.join(checkpoint_path, weight_file)
            print(f"\n📦 检查文件: {weight_file}")
            result = check_single_weight_file(file_path)
            if result:
                found_projections = True

        return found_projections
    else:
        # 单个文件
        return check_single_weight_file(checkpoint_path)

def check_single_weight_file(file_path):
    """检查单个权重文件"""
    try:
        print(f"📦 加载文件: {os.path.basename(file_path)}")

        if file_path.endswith('.safetensors'):
            from safetensors import safe_open
            checkpoint = {}
            with safe_open(file_path, framework="pt", device="cpu") as f:
                for key in f.keys():
                    checkpoint[key] = f.get_tensor(key)
        else:
            checkpoint = torch.load(file_path, map_location="cpu", weights_only=True)
        
        print(f"✅ 成功加载checkpoint")
        print(f"📋 Checkpoint键: {list(checkpoint.keys())}")
        
        # 查找投影层权重
        projection_weights = {}
        all_keys = list(checkpoint.keys())
        
        print(f"\n🔍 搜索投影层权重...")
        print(f"总键数量: {len(all_keys)}")
        
        # 搜索包含global_proj的键
        global_proj_keys = [key for key in all_keys if 'global_proj' in key]
        
        print(f"🎯 找到 {len(global_proj_keys)} 个global_proj相关键:")
        
        if len(global_proj_keys) == 0:
            print("❌ 没有找到任何global_proj权重！")
            print("💡 这说明投影层权重没有被保存，可能是因为:")
            print("   1. 投影层参数没有被包含在优化器中")
            print("   2. 投影层权重始终为零，被优化过程忽略")
            print("   3. 保存机制有问题")
            
            # 检查是否有其他相关的键
            print(f"\n🔍 检查其他可能相关的键...")
            attn_keys = [key for key in all_keys if 'attn1' in key and 'processor' in key]
            print(f"包含'attn1'和'processor'的键: {len(attn_keys)}")
            for key in attn_keys[:5]:  # 只显示前5个
                print(f"  {key}")
            if len(attn_keys) > 5:
                print(f"  ... 还有 {len(attn_keys) - 5} 个")
                
            return False
        
        # 分析每个投影层权重
        for key in global_proj_keys:
            weight = checkpoint[key]
            
            print(f"\n📊 {key}:")
            print(f"  形状: {weight.shape}")
            print(f"  数据类型: {weight.dtype}")
            print(f"  设备: {weight.device if hasattr(weight, 'device') else 'CPU'}")
            print(f"  范围: [{weight.min().item():.6f}, {weight.max().item():.6f}]")
            print(f"  均值: {weight.mean().item():.6f}")
            print(f"  标准差: {weight.std().item():.6f}")
            print(f"  零值比例: {(weight == 0).float().mean().item():.4f}")
            
            # 判断权重状态
            if weight.abs().max().item() < 1e-6:
                print(f"  ⚠️ 权重几乎全为零 - 可能未被训练")
            elif weight.std().item() < 1e-6:
                print(f"  ⚠️ 权重标准差很小 - 可能初始化有问题")
            else:
                print(f"  ✅ 权重看起来正常")
            
            projection_weights[key] = {
                'shape': weight.shape,
                'min': weight.min().item(),
                'max': weight.max().item(),
                'mean': weight.mean().item(),
                'std': weight.std().item(),
                'zero_ratio': (weight == 0).float().mean().item()
            }
        
        # 统计分析
        print(f"\n📈 投影层权重统计:")
        print(f"  投影层数量: {len(global_proj_keys)}")
        
        # 按类型分组
        weight_keys = [key for key in global_proj_keys if key.endswith('.weight')]
        bias_keys = [key for key in global_proj_keys if key.endswith('.bias')]
        
        print(f"  权重参数: {len(weight_keys)}")
        print(f"  偏置参数: {len(bias_keys)}")
        
        # 检查权重是否被训练过
        trained_weights = 0
        zero_weights = 0
        
        for key in weight_keys:
            weight = checkpoint[key]
            if weight.abs().max().item() > 1e-6 and weight.std().item() > 1e-6:
                trained_weights += 1
            else:
                zero_weights += 1
        
        print(f"\n🎯 训练状态分析:")
        print(f"  已训练的权重: {trained_weights}/{len(weight_keys)}")
        print(f"  零/未训练权重: {zero_weights}/{len(weight_keys)}")
        
        if trained_weights > 0:
            print(f"✅ 有 {trained_weights} 个投影层权重被训练过")
            return True
        else:
            print(f"❌ 所有投影层权重都未被训练（全为零或接近零）")
            return False
            
    except Exception as e:
        print(f"❌ 加载checkpoint失败: {e}")
        return False

def check_multiple_checkpoints():
    """检查多个checkpoint"""
    base_dir = "/home2/wangyuanpeng/project/VTON/amymodel/lem/IMAGGarment-1-main"
    
    checkpoints_to_check = [
        "checkpoints_vton_catvton_style_200k_4gpu/checkpoint-160000",
        "checkpoints_vton_catvton_style_200k_4gpu/checkpoint-180000", 
        "checkpoints_dresscode_with_clip_200k_4gpu/checkpoint-5000",
        "test_clip_training_output/checkpoint-25",
        "test_clip_training_output/checkpoint-50"
    ]
    
    results = {}
    
    for checkpoint_name in checkpoints_to_check:
        checkpoint_path = os.path.join(base_dir, checkpoint_name)
        print(f"\n" + "="*80)
        result = check_checkpoint_projection_weights(checkpoint_path)
        results[checkpoint_name] = result
    
    print(f"\n" + "="*80)
    print(f"📊 所有checkpoint检查结果:")
    for checkpoint_name, result in results.items():
        status = "✅ 有训练过的投影层" if result else "❌ 投影层未训练或不存在"
        print(f"  {checkpoint_name}: {status}")

def main():
    print("🔍 检查Checkpoint中的投影层权重")
    print("=" * 80)
    
    # 检查指定的checkpoint
    checkpoint_path = "/home2/wangyuanpeng/project/VTON/amymodel/lem/IMAGGarment-1-main/checkpoints_vton_catvton_style_200k_4gpu/checkpoint-100000"
    
    result = check_checkpoint_projection_weights(checkpoint_path)
    
    if not result:
        print(f"\n💡 建议:")
        print(f"1. 检查训练脚本是否正确包含投影层参数")
        print(f"2. 检查投影层是否被正确初始化（非零）")
        print(f"3. 检查优化器是否包含投影层参数")
        print(f"4. 重新训练并监控投影层梯度")
    
    # 检查其他相关的checkpoint
    print(f"\n🔍 检查其他相关checkpoint...")
    check_multiple_checkpoints()

if __name__ == "__main__":
    main()
