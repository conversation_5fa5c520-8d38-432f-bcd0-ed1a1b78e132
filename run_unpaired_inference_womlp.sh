#!/bin/bash

# 非配对模式推理脚本
# 使用checkpoint-100000权重进行非配对模式推理

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate IMAGGarment_standard

echo "开始非配对模式推理..."

# 设置参数
DATASET_NAME="vitonhd"
DATA_ROOT_PATH="/home2/wangyuanpeng/project/VTON/amymodel/lem/IMAGGarment-1-main/dataset_hd"
# 推理模式 (paired / unpaired)
MODE="unpaired"

# 设置checkpoint路径
CHECKPOINT_PATH="/home2/wangyuanpeng/project/VTON/amymodel/lem/IMAGGarment-1-main/checkpoints_vton_catvton_style_200k_4gpu/checkpoint-100000/catvton_attention_weights.safetensors"

# 生成带 checkpoint 名称、模式及时间戳的输出目录
CHECKPOINT_NAME=$(basename $(dirname "$CHECKPOINT_PATH"))   # e.g. checkpoint-180000
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="output_${MODE}_${CHECKPOINT_NAME}_${TIMESTAMP}"
# CLIP 全局特征已禁用

echo "配置信息:"
echo "  数据集: $DATASET_NAME"
echo "  数据路径: $DATA_ROOT_PATH"
echo "  权重路径: $CHECKPOINT_PATH"
echo "  输出目录: $OUTPUT_DIR"
echo "================================"
echo "=== 非配对模式推理配置 ==="
echo "非配对模式 (eval_pair): False"
echo "数据集名称: $DATASET_NAME"
echo "数据集路径: $DATA_ROOT_PATH"
echo "权重路径: $CHECKPOINT_PATH"
echo "输出目录: $OUTPUT_DIR"
echo "批处理大小: 1"
echo "推理步数: 100"
echo "引导尺度: 1"
echo "图像尺寸: 384x512"
echo "混合精度: no"
echo "Repaint功能: True"
echo "结果拼接: True"
echo "数据加载器workers: 0"
echo "============================================================"

# 运行推理 (默认非配对模式，不传递--eval_pair参数)
python "inference_tryon paired.py" \
    --dataset_name $DATASET_NAME \
    --data_root_path $DATA_ROOT_PATH \
    --resume_path $CHECKPOINT_PATH \
    --output_dir $OUTPUT_DIR \
    --batch_size 1 \
    --num_inference_steps 100 \
    --guidance_scale 1 \
    --width 384 \
    --height 512 \
    --mixed_precision no \
    --concat_eval_results \
    --repaint \
    --dataloader_num_workers 0

echo "非配对模式推理完成！"
echo "结果保存在: $OUTPUT_DIR"
echo "检查输出目录: $OUTPUT_DIR/vitonhd/unpaired/"
